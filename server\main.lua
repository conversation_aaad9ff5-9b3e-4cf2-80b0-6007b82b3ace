	ESX = nil
VehicleWanted = {}

TriggerEvent('esx:getSharedObject', function(obj) ESX = obj end)

-- Make sure all Vehicles are Stored on restart

MySQL.ready(function()
	ParkVehiclesInParkMeter()
end)


function ParkVehicles()
	MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = true WHERE `stored` = @stored', {
		['@stored'] = false
	}, function(rowsChanged)
		if rowsChanged > 0 then
			--print(('esx_advancedgarage: %s vehicle(s) have been stored!'):format(rowsChanged))
		end
	end)
end

function ParkVehiclesInParkMeter()
	MySQL.Async.execute('UPDATE owned_vehicles SET `garagenum` = 0, `parkmeter` = false, `stored` = true WHERE `stored` = @stored AND `parkmeter` = @parkmeter', {
		['@stored'] = false,
		['@parkmeter'] = true
	}, function(rowsChanged)
		if rowsChanged > 0 then
			--print(('esx_advancedgarage: %s vehicle(s) have been stored!'):format(rowsChanged))
		end
	end)
end

-- Get Owned Properties
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedProperties', function(source, cb)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
	local properties = {}

	MySQL.Async.fetchAll('SELECT * FROM owned_properties WHERE owner = @owner', {
		['@owner'] = xPlayer.identifier
	}, function(data)
		for _,v in pairs(data) do
			table.insert(properties, v.name)
		end
		cb(properties)
	end)
end)

-- Fetch Owned Aircrafts
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedAircrafts', function(source, cb)
	local ownedAircrafts = {}

	if Config.DontShowPoundCarsInGarage == true then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored', {
			['@owner']  = GetPlayerIdentifier(source),
			['@Type']   = 'aircraft',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAircrafts, {vehicle = vehicle, stored = v.stored, plate = v.plate})
			end
			cb(ownedAircrafts)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type', {
			['@owner']  = GetPlayerIdentifier(source),
			['@Type']   = 'aircraft',
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedAircrafts, {vehicle = vehicle, stored = v.stored, plate = v.plate})
			end
			cb(ownedAircrafts)
		end)
	end
end)

-- Fetch Owned Boats
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedBoats', function(source, cb)
	local ownedBoats = {}

	if Config.DontShowPoundCarsInGarage == true then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored', {
			['@owner']  = GetPlayerIdentifier(source),
			['@Type']   = 'boat',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedBoats, {vehicle = vehicle, stored = v.stored, plate = v.plate})
			end
			cb(ownedBoats)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type', {
			['@owner']  = GetPlayerIdentifier(source),
			['@Type']   = 'boat',
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedBoats, {vehicle = vehicle, stored = v.stored, plate = v.plate})
			end
			cb(ownedBoats)
		end)
	end
end)

-- Fetch Owned Cars
ESX.RegisterServerCallback('esx_advancedgarage:getOwnedCars', function(source, cb)
	local ownedCars = {}
	
	if Config.DontShowPoundCarsInGarage == true then
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored', {
			['@owner']  = GetPlayerIdentifiers(source)[1],
			['@Type']   = 'car',
			['@stored'] = true
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedCars, {vehicle = vehicle, stored = v.stored, plate = v.plate})
			end
			cb(ownedCars)
		end)
	else
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type', {
			['@owner']  = GetPlayerIdentifiers(source)[1],
			['@Type']   = 'car'
		}, function(data)
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedCars, {vehicle = vehicle, stored = v.stored, plate = v.plate, damage = v.damage})
			end
			cb(ownedCars)
		end)
	end
end)

--[[ Store Vehicles
ESX.RegisterServerCallback('esx_advancedgarage:storeVehicle', function (source, cb, vehicleProps)
	local ownedCars = {}
	local vehplate = vehicleProps.plate:match("^%s*(.-)%s*$")
	local vehiclemodel = vehicleProps.model
	local xPlayer = ESX.GetPlayerFromId(source)
	
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player OR LOWER(`owner`) = @gang) AND plate = @plate', {
		['@player'] = xPlayer.identifier,
		['@gang'] = string.lower(xPlayer.gang.name),
		['@plate'] = vehicleProps.plate
	}, function (result)
		if result[1] ~= nil then
			local originalvehprops = json.decode(result[1].vehicle)
			if originalvehprops.model == vehiclemodel then

				cb(true)
				
			
			-	if Config.KickPossibleCheaters == true then
					if Config.UseCustomKickMessage == true then
						print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: '..vehiclemodel..'. Original Vehicle: '..originalvehprops.model):format(GetPlayerIdentifiers(source)[1]))
						-- exports.BanSql:BanTarget(xPlayer.source, "Tried to change vehicle hash", "Cheat vehicle hash changer")
						cb(false)
					else
						print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: '..vehiclemodel..'. Original Vehicle: '..originalvehprops.model):format(GetPlayerIdentifiers(source)[1]))
						-- exports.BanSql:BanTarget(xPlayer.source, "Tried to change vehicle hash", "Cheat vehicle hash changer")
						cb(false)
					end
				else
					print(('esx_advancedgarage: %s attempted to Cheat! Tried Storing: '..vehiclemodel..'. Original Vehicle: '..originalvehprops.model):format(GetPlayerIdentifiers(source)[1]))
					cb(false)
				end
			end
		else
			print(('esx_advancedgarage: %s attempted to store an vehicle they don\'t own!'):format(GetPlayerIdentifiers(source)[1]))
			cb(false)
		end
	end)
end)
--]]

ESX.RegisterServerCallback('esx_advancedgarage:storeVehicle', function (source, cb, vehicleProps)
	local ownedCars = {}
	local vehplate = vehicleProps.plate:match("^%s*(.-)%s*$")
	local vehiclemodel = vehicleProps.model
	local xPlayer = ESX.GetPlayerFromId(source)
	
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player OR LOWER(`owner`) = @gang) AND plate = @plate', {
		['@player'] = xPlayer.identifier,
		['@gang'] = string.lower(xPlayer.gang.name),
		['@plate'] = vehicleProps.plate
	}, function (result)
		if result[1] ~= nil then
			local originalvehprops = json.decode(result[1].vehicle)
			if originalvehprops.model == vehiclemodel then
				MySQL.Async.execute('UPDATE owned_vehicles SET vehicle = @vehicle WHERE (owner = @player OR LOWER(`owner`) = @gang) AND plate = @plate', {
					['@player'] = xPlayer.identifier,
					['@gang'] = string.lower(xPlayer.gang.name),
					['@vehicle'] = json.encode(vehicleProps),
					['@plate']  = vehicleProps.plate
				}, function (rowsChanged)
					if rowsChanged == 0 then
						print(('esx_advancedgarage: %s attempted to store an vehicle they don\'t own!'):format(GetPlayerIdentifiers(source)[1]))
					end
					cb(true)
				end)
			else
				-- exports.BanSql:BanTarget(xPlayer.source, "Tried to change vehicle hash", "Cheat vehicle hash changer")
				cb(false)
			end
		else
			print(('esx_advancedgarage: %s attempted to store an vehicle they don\'t own!'):format(GetPlayerIdentifiers(source)[1]))
			cb(false)
		end
	end)
end)

-- Fetch Pounded Aircrafts
ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedAircrafts', function(source, cb)
	local ownedAircrafts = {}

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored', {
		['@owner'] = GetPlayerIdentifier(source),
		['@Type']   = 'aircraft',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedAircrafts, vehicle)
		end
		cb(ownedAircrafts)
	end)
end)

-- Fetch Pounded Boats
ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedBoats', function(source, cb)
	local ownedBoats = {}
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND Type = @Type AND `stored` = @stored', {
		['@owner'] = GetPlayerIdentifier(source),
		['@Type']   = 'boat',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedBoats, vehicle)
		end
		cb(ownedBoats)
	end)
end)

-- Fetch Pounded Cars
ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedCars', function(source, cb)
	local ownedCars = {}
	xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player or LOWER(`owner`) = @gang) AND Type = @Type AND `stored` = @stored', {
		['@player'] = xPlayer.identifier,
		['@gang'] 	= string.lower(xPlayer.gang.name),
		['@Type']   = 'car',
		['@stored'] = false
	}, function(data)
		if data then
			for _,v in pairs(data) do
				local vehicle = json.decode(v.vehicle)
				table.insert(ownedCars, {vehicle = vehicle, police = v.police})
			end
		end
		cb(ownedCars)
	end)
end)

-- Fetch Pounded Cars by police
ESX.RegisterServerCallback('esx_advancedgarage:getPoundedPolice', function(source, cb, target)
	local ownedCars = {}
	xPlayer = ESX.GetPlayerFromId(target)
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player or LOWER(`owner`) = @gang) AND Type = @Type AND `stored` = @stored AND police = true', {
		['@player'] = xPlayer.identifier,
		['@gang'] 	= string.lower(xPlayer.gang.name),
		['@Type']   = 'car',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedCars, vehicle)
		end
		cb(ownedCars)
	end)
end)



-- Fetch Pounded Policing Vehicles
ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedPolicingCars', function(source, cb)
	local ownedPolicingCars = {}

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = GetPlayerIdentifiers(source)[1],
		['@job']    = 'police',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedPolicingCars, vehicle)
		end
		cb(ownedPolicingCars)
	end)
end)

-- Fetch Pounded Ambulance Vehicles
ESX.RegisterServerCallback('esx_advancedgarage:getOutOwnedAmbulanceCars', function(source, cb)
	local ownedAmbulanceCars = {}

	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE owner = @owner AND job = @job AND `stored` = @stored', {
		['@owner'] = GetPlayerIdentifiers(source)[1],
		['@job']    = 'ambulance',
		['@stored'] = false
	}, function(data) 
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedAmbulanceCars, vehicle)
		end
		cb(ownedAmbulanceCars)
	end)
end)

-- Check Money for Pounded Aircrafts
ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyAircrafts', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= Config.AircraftPoundPrice then
		cb(true)
	else
		cb(false)
	end
end)

-- Check Money for Pounded Boats
ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyBoats', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= Config.BoatPoundPrice then
		cb(true)
	else
		cb(false)
	end
end)

-- Check Money for Pounded Cars
ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyCars', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= Config.CarPoundPrice then
		cb(true)
	else
		cb(false)
	end
end)

-- Check Repair cost fee
ESX.RegisterServerCallback('esx_advancedgarage:checkRepairCost', function(source, cb, fee)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= fee then
		cb(true)
	else
		cb(false)
	end
end)
-- Check Money for Pounded Policing
ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyPolicing', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= Config.PolicingPoundPrice then
		cb(true)
	else
		cb(false)
	end
end)

-- Check Money for Pounded Ambulance
ESX.RegisterServerCallback('esx_advancedgarage:checkMoneyAmbulance', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	if xPlayer.get('money') >= Config.AmbulancePoundPrice then
		cb(true)
	else
		cb(false)
	end
end)

-- Pay for Pounded Aircrafts
RegisterServerEvent('esx_advancedgarage:payAircraft')
AddEventHandler('esx_advancedgarage:payAircraft', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.AircraftPoundPrice)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.AircraftPoundPrice)
end)

-- Pay for Pounded Boats
RegisterServerEvent('esx_advancedgarage:payBoat')
AddEventHandler('esx_advancedgarage:payBoat', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.BoatPoundPrice)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.BoatPoundPrice)
end)

RegisterServerEvent('esx_advancedgarage:ResponseFindVehicle')
AddEventHandler('esx_advancedgarage:ResponseFindVehicle', function(accessible, plate)
	VehicleWanted[plate].setState(accessible)
end)

RegisterServerEvent('esx_advancedgarage:StartFindingVehicle')
AddEventHandler('esx_advancedgarage:StartFindingVehicle', function(plate)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(source)
	if not VehicleWanted[plate] then
		xPlayer.removeMoney(Config.CarPoundPrice)
		VehicleWanted[plate] = CreateVehicleWanted(plate, _source)
		TriggerClientEvent('esx:showNotification', source, 'Shoma Mablaqe ' .. Config.CarPoundPrice .. ' Pardakht Kardid, Ma Dar Hale Jostojoye Baraye Mashine Shoma Hastim!')
		TriggerEvent('esx_addonaccount:getSharedAccount', 'society_police', function(account)
			account.addMoney(Config.CarPoundPrice)
		end)
		DeleteEVehicleByPlate(plate)
		MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE plate = @plate AND `parkmeter` = @parkmeter', {
			['@parkmeter'] = true,
			['@plate'] = plate,
		}, function(data)
			if data[1] ~= nil then
				TriggerClientEvent("esx_addonaccount:RemoveParkMeter", xPlayer.source, data[1].garagenum)
				exports.ghmattimysql:execute('UPDATE `owned_vehicles` SET `parkmeter` = @parkmeter, `garagenum` = @garagenum WHERE `plate` = @plate AND `parkmeter` = @parked',{
					['@plate']  = plate,
					['@parked'] = true,
					['@parkmeter'] = false,
					['@garagenum'] = 0,
				})
			end
		end)

		TriggerClientEvent('esx_advancedgarage:FindVehicle', -1, plate)
	else
		TriggerClientEvent('esx:showNotification', source, 'Mashin shoma dar list mashin haye gom shode sabt shode, lotfan sabor bashid')
	end
end)

-- Pay for Pounded Policing
RegisterServerEvent('esx_advancedgarage:payPolicing')
AddEventHandler('esx_advancedgarage:payPolicing', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.PolicingPoundPrice)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.PolicingPoundPrice)
end)

-- Pay for Pounded Ambulance
RegisterServerEvent('esx_advancedgarage:payAmbulance')
AddEventHandler('esx_advancedgarage:payAmbulance', function()
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(Config.AmbulancePoundPrice)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. Config.AmbulancePoundPrice)
end)

-- Pay to Return Broken Vehicles
RegisterServerEvent('esx_advancedgarage:payhealth')
AddEventHandler('esx_advancedgarage:payhealth', function(price)
	local xPlayer = ESX.GetPlayerFromId(source)
	xPlayer.removeMoney(price)
	TriggerClientEvent('esx:showNotification', source, _U('you_paid') .. price)
	TriggerEvent('esx_addonaccount:getSharedAccount', 'society_mecano', function(account)
		account.addMoney(price)
	end)
end)

-- Modify State of Vehicles
RegisterServerEvent('esx_advancedgarage:setVehicleState')
AddEventHandler('esx_advancedgarage:setVehicleState', function(plate, state, damage)
	local xPlayer = ESX.GetPlayerFromId(source)

	MySQL.Async.execute('UPDATE owned_vehicles SET `stored` = @stored, `damage` = @damage WHERE plate = @plate', {
		['@stored'] = state,
		['@plate'] = plate,
		['@damage'] = damage
	}, function(rowsChanged)
		if rowsChanged == 0 then
			--print(('esx_advancedgarage: %s exploited the garage!'):format(xPlayer.identifier))
		end
	end)
end)

AddEventHandler('esx:playerDropped', function(source)
	local _source = source
	if _source then
		local identifier = GetPlayerIdentifier(_source)
		local Player = ESX.GetPlayerFromIdentifier(identifier)
		MySQL.Async.execute('UPDATE owned_vehicles SET parkmeter = false, stored = true, garagenum = 0 WHERE (owner = @player or owner = @gang) AND parkmeter = true AND stored = false', {
			['@player'] = identifier,
			['@gang']  	= Player.gang.name,
		}, function(data)
			if data == 0 then
				--print("ERROR : Dropped Error")
			end
		end)
	end
end)

RegisterServerEvent('ParkMeter:Set')
AddEventHandler('ParkMeter:Set', function(state, num, plate, damage)
	local xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.execute('UPDATE owned_vehicles SET `damage` = @damage, `garagenum` = @garagenum, `parkmeter` = @parkmeter WHERE plate = @plate', {
		['@plate'] = plate,
		['@garagenum'] = num,
		['@parkmeter'] = state,
		['@damage'] = damage
	}, function(data)
		if data == 0 then
			print("ERROR: Not Seted")
		end
	end)
end)

ESX.RegisterServerCallback('ParkMeter:Spawn', function(source, cb, ParkNum)
	local xPlayer = ESX.GetPlayerFromId(source)
	local ownedCars = {}
	MySQL.Async.fetchAll('SELECT * FROM owned_vehicles WHERE (owner = @player or owner = @gang) AND type = @Type AND `parkmeter` = @parkmeter AND `garagenum` = @garagenum', {
		['@player'] = GetPlayerIdentifiers(source)[1],
		['@gang']  	= xPlayer.gang.name,
		['@Type']   = 'car',
		['@parkmeter'] = true,
		['@garagenum'] = ParkNum,
		['@plate'] = plate,
		['@damage'] = damage
	}, function(data)
		for _,v in pairs(data) do
			local vehicle = json.decode(v.vehicle)
			table.insert(ownedCars, {vehicle = vehicle, plate = v.plate, damage = v.damage})
		end
		cb(ownedCars)
	end)
end)

local mecano = 0
function CountMC()
	local xPlayers = ESX.GetPlayers()
	mecano = 0
	for i=1, #xPlayers, 1 do
		local xPlayer = ESX.GetPlayerFromId(xPlayers[i])
		if xPlayer.job.name == 'mecano' then
			mecano = mecano + 1
		end
	end
	SetTimeout(10 * 1000, CountMC)
end

CountMC()

ESX.RegisterServerCallback('esx_advancedgarage:mecanolive', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	cb(mecano)
end)